export enum CollectionStatus {
  PRELAUNCH = "PRELAUNCH",
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  collectionId: string;
  name: string;
  logoUrl: string;
  description: string;
  status: CollectionStatus;
  createdAt: Date;
  updatedAt: Date;
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PRELAUNCH]: "Pre-launch",
  [CollectionStatus.ACTIVE]: "Active",
  [CollectionStatus.DELETED]: "Deleted",
};

export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  name?: string;
  role?: "admin" | "user";
  tg_id?: string;
  ton_wallet_address?: string;
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderEntity {
  id?: string;
  number?: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId: string;
  productId: string;
  amount: number;
  status: OrderStatus;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TxLookup {
  last_checked_record_id: string;
}
