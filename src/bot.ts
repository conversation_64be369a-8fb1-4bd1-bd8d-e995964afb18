import { Telegraf, Mark<PERSON> } from "telegraf";
import dotenv from "dotenv";
import {
  getUserOrdersByTgId,
  completePurchaseByBot,
  formatOrderForDisplay,
  getCompletableOrders,
} from "./firebase-service";

// Load environment variables
dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;
const WEB_APP_URL =
  process.env.WEB_APP_URL ?? "https://4d5rqhd0-3000.euw.devtunnels.ms/";

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

// Create bot instance
const bot = new Telegraf(BOT_TOKEN);

// Simple in-memory session store for pending orders
const userSessions = new Map<string, { pendingOrderId?: string }>();

// Start command handler
bot.start((ctx) => {
  const welcomeMessage = `
🛍️ Welcome to the Marketplace Bot!

This bot helps you access our marketplace platform. Use the buttons below to get started or open the full marketplace using the menu button.
  `;

  ctx.reply(
    welcomeMessage,
    Markup.keyboard([
      [Markup.button.text("👋 Hello World")],
      [Markup.button.text("📋 Get My Orders")],
      [Markup.button.text("✅ Complete Order")],
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ]).resize()
  );
});

// Hello World button handler
bot.hears("👋 Hello World", (ctx) => {
  ctx.reply(
    "👋 Hello World! Welcome to our marketplace bot.\n\n" +
      "This is a simple greeting message. You can use this bot to:\n" +
      "• Access the marketplace\n" +
      "• Complete orders\n" +
      "• Get support\n\n" +
      "Use the menu button above to open the full marketplace experience!",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Get My Orders button handler
bot.hears("📋 Get My Orders", async (ctx) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🔍 Fetching your orders...");

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.orders.length === 0) {
      ctx.reply(
        "📭 You don't have any orders yet.\n\n" +
          "Create your first order using the marketplace web app!",
        Markup.inlineKeyboard([
          [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
        ])
      );
      return;
    }

    const orders = ordersResponse.orders;
    const completableOrders = getCompletableOrders(orders);

    let message = `📋 Your Orders (${orders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    // Create inline keyboard with order buttons
    const orderButtons = orders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    // Add navigation buttons
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (orders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error fetching user orders:", error);
    ctx.reply(
      "❌ Failed to fetch your orders. Please try again later.",
      Markup.inlineKeyboard([
        [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
      ])
    );
  }
});

// Complete Order button handler
bot.hears("✅ Complete Order", (ctx) => {
  ctx.reply(
    "📦 Complete Order\n\n" +
      "To complete an order, please use the marketplace web app where you can:\n" +
      "• View your active orders\n" +
      "• Track order status\n" +
      "• Complete payment\n" +
      "• Leave feedback\n\n" +
      "Click the button below to open the marketplace:",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
      [Markup.button.callback("📋 Order Help", "order_help")],
    ])
  );
});

// Order help callback handler
bot.action("order_help", (ctx) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📋 Order Help\n\n" +
      "If you need help with your order:\n\n" +
      "1. Open the marketplace using the menu button\n" +
      "2. Go to your profile/orders section\n" +
      "3. Find your order and follow the instructions\n" +
      "4. Contact support if you encounter issues\n\n" +
      "For immediate assistance, you can also contact our support team.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
      [Markup.button.callback("📞 Contact Support", "contact_support")],
    ])
  );
});

// Contact support callback handler
bot.action("contact_support", (ctx) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📞 Contact Support\n\n" +
      "You can reach our support team through:\n\n" +
      "• Email: <EMAIL>\n" +
      "• Telegram: @marketplace_support\n" +
      "• Live chat in the web app\n\n" +
      "We typically respond within 24 hours.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Open marketplace callback handler
bot.action("open_marketplace", (ctx) => {
  ctx.answerCbQuery();
  ctx.reply(
    "🌐 Opening Marketplace\n\n" +
      "Click the button below to open the full marketplace experience:",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Order selection callback handler
bot.action(/^order_(.+)$/, async (ctx) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];
    const tgId = ctx.from?.id?.toString();

    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    // Get user orders to find the selected order
    const ordersResponse = await getUserOrdersByTgId(tgId);
    const order = ordersResponse.orders.find((o) => o.id === orderId);

    if (!order) {
      ctx.reply("❌ Order not found. It may have been completed or cancelled.");
      return;
    }

    let message = `📦 Order #${order.number}\n\n`;
    message += `${formatOrderForDisplay(order)}\n\n`;

    if (order.status === "paid" && order.buyerId) {
      message += "🎁 This order is ready for completion!\n\n";
      message += "To complete this order:\n";
      message += "1. Send the gift/item to this bot\n";
      message += "2. The bot will verify and complete the purchase\n";
      message += "3. Funds will be transferred to the seller\n\n";
      message +=
        "⚠️ Only send the gift when you're ready to complete the order!";

      ctx.reply(
        message,
        Markup.inlineKeyboard([
          [
            Markup.button.callback(
              "🎁 I'm ready to send gift",
              `complete_${orderId}`
            ),
          ],
          [Markup.button.callback("🔙 Back to Orders", "back_to_orders")],
          [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
        ])
      );
    } else {
      message += `Status: ${order.status.toUpperCase()}\n\n`;

      if (order.status === "active") {
        message += "This order is waiting for a buyer.";
      } else if (order.status === "fulfilled") {
        message += "This order has been completed.";
      } else if (order.status === "cancelled") {
        message += "This order has been cancelled.";
      }

      ctx.reply(
        message,
        Markup.inlineKeyboard([
          [Markup.button.callback("🔙 Back to Orders", "back_to_orders")],
          [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
        ])
      );
    }
  } catch (error) {
    console.error("Error handling order selection:", error);
    ctx.reply("❌ Failed to load order details. Please try again later.");
  }
});

// Back to orders callback handler
bot.action("back_to_orders", async (ctx) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    // Trigger the same logic as "Get My Orders" button
    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.orders.length === 0) {
      ctx.reply(
        "📭 You don't have any orders yet.\n\n" +
          "Create your first order using the marketplace web app!",
        Markup.inlineKeyboard([
          [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
        ])
      );
      return;
    }

    const orders = ordersResponse.orders;
    const completableOrders = getCompletableOrders(orders);

    let message = `📋 Your Orders (${orders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    const orderButtons = orders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (orders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error going back to orders:", error);
    ctx.reply("❌ Failed to load orders. Please try again later.");
  }
});

// Order completion callback handler
bot.action(/^complete_(.+)$/, async (ctx) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];

    ctx.reply(
      "🎁 Ready to Complete Order\n\n" +
        "Please send the gift/item to this bot now. You can send:\n" +
        "• Photos\n" +
        "• Documents\n" +
        "• Text messages\n" +
        "• Any other content\n\n" +
        "⚠️ Once you send the gift, the order will be automatically completed and funds will be transferred!",
      Markup.inlineKeyboard([
        [Markup.button.callback("❌ Cancel", `order_${orderId}`)],
      ])
    );

    // Store the order ID for the next message from this user
    const userId = ctx.from?.id?.toString();
    if (userId && orderId) {
      userSessions.set(userId, { pendingOrderId: orderId });
    }
  } catch (error) {
    console.error("Error handling order completion:", error);
    ctx.reply("❌ Failed to prepare order completion. Please try again later.");
  }
});

// Handle any message (gift) when user has a pending order
bot.on("message", async (ctx) => {
  try {
    const userId = ctx.from?.id?.toString();
    if (!userId) return;

    const session = userSessions.get(userId);
    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      // No pending order, ignore the message
      return;
    }

    // Clear the pending order
    userSessions.delete(userId);

    ctx.reply("🔄 Processing your gift and completing the order...");

    try {
      const result = await completePurchaseByBot(pendingOrderId);

      if (result.success) {
        ctx.reply(
          "✅ Order Completed Successfully!\n\n" +
            `📦 Order #${result.order.number}\n` +
            `💰 Seller received: ${result.netAmountToSeller} TON\n` +
            `💸 Fee applied: ${result.feeAmount} TON\n\n` +
            "🎉 Thank you for using our marketplace!",
          Markup.inlineKeyboard([
            [Markup.button.callback("📋 View My Orders", "back_to_orders")],
            [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
          ])
        );
      } else {
        throw new Error("Purchase completion failed");
      }
    } catch (completionError) {
      console.error("Error completing purchase:", completionError);

      // Return the gift to the user
      ctx.reply(
        "❌ Failed to complete the order. Your gift is being returned to you.\n\n" +
          "Error: " +
          (completionError as Error).message +
          "\n\n" +
          "Please try again later or contact support.",
        Markup.inlineKeyboard([
          [Markup.button.callback("📋 View My Orders", "back_to_orders")],
          [Markup.button.callback("📞 Contact Support", "contact_support")],
        ])
      );

      // Forward the original message back to the user
      if (ctx.message) {
        try {
          await ctx.forwardMessage(ctx.from.id);
        } catch (forwardError) {
          console.error("Error forwarding message back:", forwardError);
          ctx.reply(
            "⚠️ Unable to return your gift automatically. Please save it manually."
          );
        }
      }
    }
  } catch (error) {
    console.error("Error handling message:", error);
    ctx.reply("❌ An error occurred while processing your message.");
  }
});

// Help command
bot.help((ctx) => {
  ctx.reply(
    "🤖 Marketplace Bot Help\n\n" +
      "Available commands:\n" +
      "/start - Start the bot and show main menu\n" +
      "/help - Show this help message\n\n" +
      "Available buttons:\n" +
      "👋 Hello World - Get a welcome message\n" +
      "📋 Get My Orders - View and manage your orders\n" +
      "✅ Complete Order - Get help with completing orders\n\n" +
      "Order completion flow:\n" +
      "1. Use 'Get My Orders' to see orders ready for completion\n" +
      "2. Click on a paid order to start completion\n" +
      "3. Send the gift/item to this bot\n" +
      "4. The bot will automatically complete the purchase\n\n" +
      "You can also use the menu button to open the full marketplace web app.",
    Markup.inlineKeyboard([
      [Markup.button.webApp("🌐 Open Marketplace", WEB_APP_URL)],
    ])
  );
});

// Error handling
bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx.reply("Sorry, something went wrong. Please try again later.");
});

export default bot;
