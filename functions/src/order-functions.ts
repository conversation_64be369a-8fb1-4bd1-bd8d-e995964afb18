import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { OrderEntity } from "./types";
import {
  hasAvailableBalance,
  lockFunds,
  unlockFunds,
  spendLockedFunds,
  updateUserBalance,
} from "./balance-service";
import { applyRejectOrderFee, applyPurchaseFee } from "./fee-service";
import { getNextCounterValue } from "./counter-service";
import { verifyBotToken } from "./bot-auth-service";

export const createOrder = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { sellerId, productId, amount } = data;

  if (!sellerId || !productId || !amount || amount <= 0) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "sellerId, productId, and valid amount are required."
    );
  }

  if (context.auth.uid !== sellerId) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "You can only create orders for yourself as seller."
    );
  }

  try {
    const db = admin.firestore();

    const hasBalance = await hasAvailableBalance(sellerId, amount);
    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Insufficient available balance to create order."
      );
    }

    await lockFunds(sellerId, amount);

    // Get next order number
    const orderNumber = await getNextCounterValue("order_number");

    const orderData: Omit<OrderEntity, "id"> = {
      number: orderNumber,
      sellerId,
      productId,
      amount,
      status: "active",
      createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
    };

    const orderRef = await db.collection("orders").add(orderData);

    return {
      success: true,
      orderId: orderRef.id,
      message: `Order created successfully with ${amount} TON locked`,
    };
  } catch (error) {
    console.error("Error creating order:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchase = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const db = admin.firestore();
    const buyerId = context.auth.uid;

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status
    if (order.status !== "active") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order is not available for purchase."
      );
    }

    // Check if buyer is not the seller
    if (buyerId === order.sellerId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You cannot purchase your own order."
      );
    }

    // Check if buyer has sufficient balance
    const hasBalance = await hasAvailableBalance(buyerId, order.amount);
    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Insufficient available balance to make purchase."
      );
    }

    // Lock buyer's funds (fee will be applied when purchase is completed)
    await lockFunds(buyerId, order.amount);

    // Update order status and add buyer
    await db.collection("orders").doc(orderId).update({
      buyerId,
      status: "paid",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Purchase completed successfully for ${order.amount} TON`,
    };
  } catch (error) {
    console.error("Error making purchase:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});

export const rejectPurchase = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { orderId } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = context.auth.uid;

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if order can be rejected (active or paid status)
    if (order.status !== "active" && order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order cannot be rejected in current status."
      );
    }

    // Check if user is either buyer or seller
    const isSeller = userId === order.sellerId;
    const isBuyer = order.buyerId && userId === order.buyerId;

    if (!isSeller && !isBuyer) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only buyer or seller can reject the order."
      );
    }

    // Apply rejection fee to the person who rejects
    await applyRejectOrderFee(userId, order.amount);

    // Unlock funds for the rejector
    await unlockFunds(userId, order.amount);

    // If there's a buyer and order is paid, unlock buyer's funds too
    if (order.status === "paid" && order.buyerId && order.buyerId !== userId) {
      await unlockFunds(order.buyerId, order.amount);
    }

    // If buyer is rejecting, delete the order completely
    if (isBuyer) {
      await db.collection("orders").doc(orderId).delete();
      return {
        success: true,
        message:
          "Order rejected and deleted successfully. Rejection fee applied.",
      };
    }

    // If seller is rejecting, just cancel the order
    await db.collection("orders").doc(orderId).update({
      status: "cancelled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: "Order rejected successfully. Rejection fee applied.",
    };
  } catch (error) {
    console.error("Error rejecting purchase:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while rejecting purchase."
    );
  }
});

export const completePurchase = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Get order
      const orderDoc = await db.collection("orders").doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError("not-found", "Order not found.");
      }

      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Check order status - must be paid to complete
      if (order.status !== "paid") {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order must be in 'paid' status to complete."
        );
      }

      // Check if order has a buyer
      if (!order.buyerId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Order has no buyer."
        );
      }

      // Apply purchase fee to buyer's locked funds and get net amount
      const feeAmount = await applyPurchaseFee(order.buyerId, order.amount);
      const netAmountToSeller = order.amount - feeAmount;

      // Spend buyer's locked funds (removes from both sum and locked)
      await spendLockedFunds(order.buyerId, order.amount);

      // Unlock seller's funds
      await unlockFunds(order.sellerId, order.amount);

      // Transfer net amount to seller
      await updateUserBalance(order.sellerId, netAmountToSeller, 0);

      // Update order status to fulfilled
      await db.collection("orders").doc(orderId).update({
        status: "fulfilled",
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      return {
        success: true,
        message: `Purchase completed successfully. Seller received ${netAmountToSeller} TON (${feeAmount} TON fee applied)`,
        netAmountToSeller,
        feeAmount,
      };
    } catch (error) {
      console.error("Error completing purchase:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while completing purchase."
      );
    }
  }
);

export const getOrdersByProductId = functions.https.onCall(
  async (data, context) => {
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required."
      );
    }

    const { productId } = data;

    if (!productId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Product ID is required."
      );
    }

    try {
      const db = admin.firestore();

      // Query orders by productId with status "paid" (ready for completion)
      const ordersQuery = await db
        .collection("orders")
        .where("productId", "==", productId)
        .where("status", "==", "paid")
        .get();

      const orders: OrderEntity[] = [];
      ordersQuery.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
      });

      return {
        success: true,
        orders,
        count: orders.length,
      };
    } catch (error) {
      console.error("Error getting orders by product ID:", error);
      throw new functions.https.HttpsError(
        "internal",
        (error as any).message ?? "Server error while getting orders."
      );
    }
  }
);

export const getUserOrders = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { userId, tgId } = data;
  const requesterId = context.auth.uid;

  try {
    const db = admin.firestore();
    let targetUserId = userId;

    // If tgId is provided, find user by Telegram ID
    if (tgId && !userId) {
      const userQuery = await db
        .collection("users")
        .where("tg_id", "==", tgId.toString())
        .limit(1)
        .get();

      if (userQuery.empty) {
        throw new functions.https.HttpsError(
          "not-found",
          "User not found with provided Telegram ID."
        );
      }

      targetUserId = userQuery.docs[0].id;
    }

    // If no userId or tgId provided, use the authenticated user's ID
    if (!targetUserId) {
      targetUserId = requesterId;
    }

    // Check if requester has permission to view these orders
    // Users can only view their own orders unless they're admin
    if (targetUserId !== requesterId) {
      const requesterDoc = await db.collection("users").doc(requesterId).get();
      const requesterData = requesterDoc.data();

      if (!requesterData || requesterData.role !== "admin") {
        throw new functions.https.HttpsError(
          "permission-denied",
          "You can only view your own orders."
        );
      }
    }

    // Get orders where user is either buyer or seller
    const [sellerOrdersQuery, buyerOrdersQuery] = await Promise.all([
      db.collection("orders").where("sellerId", "==", targetUserId).get(),
      db.collection("orders").where("buyerId", "==", targetUserId).get(),
    ]);

    const orders: OrderEntity[] = [];
    const orderIds = new Set<string>();

    // Add seller orders
    sellerOrdersQuery.forEach((doc) => {
      if (!orderIds.has(doc.id)) {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
        orderIds.add(doc.id);
      }
    });

    // Add buyer orders (avoid duplicates)
    buyerOrdersQuery.forEach((doc) => {
      if (!orderIds.has(doc.id)) {
        orders.push({ id: doc.id, ...doc.data() } as OrderEntity);
        orderIds.add(doc.id);
      }
    });

    // Sort orders by creation date (newest first)
    orders.sort((a, b) => {
      const aTime = a.createdAt?.toMillis() || 0;
      const bTime = b.createdAt?.toMillis() || 0;
      return bTime - aTime;
    });

    return {
      success: true,
      orders,
      count: orders.length,
      userId: targetUserId,
    };
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while getting user orders."
    );
  }
});

export const completePurchaseByBot = functions.https.onCall(async (data) => {
  const { orderId, botToken } = data;

  if (!orderId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Order ID is required."
    );
  }

  if (!botToken) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Bot token is required."
    );
  }

  // Verify bot token
  if (!verifyBotToken(botToken)) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Invalid bot token."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check order status - must be paid to complete
    if (order.status !== "paid") {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order must be in 'paid' status to complete."
      );
    }

    // Check if order has a buyer
    if (!order.buyerId) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Order has no buyer."
      );
    }

    // Apply purchase fee to buyer's locked funds and get net amount
    const feeAmount = await applyPurchaseFee(order.buyerId, order.amount);
    const netAmountToSeller = order.amount - feeAmount;

    // Spend buyer's locked funds (removes from both sum and locked)
    await spendLockedFunds(order.buyerId, order.amount);

    // Unlock seller's funds
    await unlockFunds(order.sellerId, order.amount);

    // Transfer net amount to seller
    await updateUserBalance(order.sellerId, netAmountToSeller, 0);

    // Update order status to fulfilled
    await db.collection("orders").doc(orderId).update({
      status: "fulfilled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Purchase completed successfully by bot. Seller received ${netAmountToSeller} TON (${feeAmount} TON fee applied)`,
      netAmountToSeller,
      feeAmount,
      order: {
        id: order.id,
        number: order.number,
        status: "fulfilled",
      },
    };
  } catch (error) {
    console.error("Error completing purchase by bot:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while completing purchase."
    );
  }
});
